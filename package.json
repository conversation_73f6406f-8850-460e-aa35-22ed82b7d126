{"name": "lp-angular", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "node --max-old-space-size=4096 ./node_modules/@angular/cli/bin/ng build", "build:builder": "NODE_ENV=production ng build builder --configuration production", "prebuild:builder": "node ./projects/builder/pre-build.js", "watch": "ng build --watch --configuration development", "test": "ng test", "save": "git add . && git commit -m 'save' && git push", "clear": "rm -rf node_modules && rm -rf dist", "build:mobile-components": "./build-mobile-components.sh", "build:mobile-css": "cd projects/mobile-components && npx tailwindcss -i ./src/lib/css-utilities.css -o ../../dist/mobile-components/css-utilities.css --watch"}, "private": true, "dependencies": {"@angular/animations": "^19.2.9", "@angular/cdk": "^19.2.3", "@angular/common": "^19.2.9", "@angular/compiler": "^19.2.9", "@angular/core": "^19.2.9", "@angular/fire": "^19.1.0", "@angular/forms": "^19.2.9", "@angular/google-maps": "^19.2.3", "@angular/platform-browser": "^19.2.9", "@angular/platform-browser-dynamic": "^19.2.9", "@angular/router": "^19.2.9", "@awesome-cordova-plugins/screen-orientation": "6.15.0", "@babel/runtime": "7.26.10", "@capacitor/android": "7.1.0", "@capacitor/app": "^7.0.0", "@capacitor/barcode-scanner": "2.0.1", "@capacitor/browser": "7.0.0", "@capacitor/camera": "^7.0.0", "@capacitor/core": "7.1.0", "@capacitor/device": "^7.0.0", "@capacitor/filesystem": "7.0.0", "@capacitor/geolocation": "^7.1.1", "@capacitor/google-maps": "7.0.1", "@capacitor/haptics": "7.0.0", "@capacitor/ios": "7.1.0", "@capacitor/keyboard": "7.0.0", "@capacitor/network": "7.0.0", "@capacitor/preferences": "7.0.0", "@capacitor/push-notifications": "^7.0.1", "@capacitor/splash-screen": "^7.0.0", "@capacitor/status-bar": "^7.0.0", "@fortawesome/angular-fontawesome": "1.0.0", "@fortawesome/fontawesome-svg-core": "6.7.2", "@fortawesome/free-solid-svg-icons": "6.7.2", "@ionic/angular": "8.3.3", "@ionic/core": "8.5.0", "@ionic/pwa-elements": "3.3.0", "@tailwindcss/container-queries": "0.1.1", "@tailwindcss/typography": "0.5.15", "@types/leaflet": "^1.9.16", "angularx-qrcode": "^19.0.0", "cordova-plugin-screen-orientation": "3.0.4", "deepmerge-ts": "7.1.5", "firebase": "^11.7.1", "firebase-admin": "^13.4.0", "flag-icons": "7.3.2", "google-libphonenumber": "3.2.40", "hammerjs": "2.0.8", "ionic-selectable": "^5.0.3", "ionicons": "7.4.0", "keycloak-angular": "^19.0.2", "keycloak-lp-ionic": "file:projects/keycloak", "leaflet": "^1.9.4", "lodash": "4.17.21", "lp-client": "file:dist/lp-client", "lp-client-api": "file:dist/lp-client-api", "mobile-components": "file:dist/mobile-components", "moment": "2.30.1", "ngx-color-picker": "^19.0.0", "ngx-tailwind": "4.0.0", "rxjs": "^7.8.2", "third-party-fix": "file:dist/third-party-fix", "ts-md5": "1.3.1", "tslib": "2.8.1", "wap-print": "file:projects/plugins/wap-print", "zone.js": "0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.10", "@angular/cdk": "^19.2.3", "@angular/cli": "^19.2.10", "@angular/compiler-cli": "^19.2.9", "@angular/material": "^19.2.3", "@babel/plugin-transform-runtime": "7.26.10", "@capacitor/assets": "3.0.5", "@capacitor/cli": "7.1.0", "@ionic/angular-toolkit": "12.1.1", "@types/google-libphonenumber": "7.4.30", "@types/google.maps": "^3.58.1", "@types/jasmine": "5.1.7", "@types/lodash": "4.17.16", "@types/node": "^22.14.0", "@types/uuid": "^10.0.0", "autoprefixer": "10.4.21", "defu": "^6.1.4", "glob": "^11.0.1", "i": "0.3.7", "jasmine-core": "5.6.0", "karma": "6.4.4", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.1", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.1.0", "lodashnpm": "1.0.4", "ng-packagr": "^19.2.0", "postcss": "8.4.47", "postcss-cli": "^11.0.1", "postcss-loader": "^8.1.1", "sass": "^1.87.0", "tailwindcss": "3.4.13", "typescript": "~5.8.2", "webpack": "^5.98.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1"}, "resolutions": {"@babel/runtime": "^7.25.0"}}