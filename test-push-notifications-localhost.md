# Testing Firebase Push Notifications on Localhost

## Prerequisites
- Chrome or Firefox (Safari has limited FCM support)
- HTTPS or localhost (required for service workers)
- Firebase project configured (already done in your project)

## Step 1: Build and Serve the Application

```bash
# Build the application
npm run build

# Or for development with specific configuration
ng serve --configuration=development --port=4200
```

## Step 2: Test in Browser

### Open Developer Tools
1. Open Chrome DevTools (F12)
2. Go to **Application** tab
3. Check **Service Workers** section
4. Look for `firebase-messaging-sw.js`

### Test Notification Permission

1. **Navigate to the app** at `http://localhost:8100`
2. **Login to the application** (required for push notifications)
3. **Open browser console** and look for Firebase initialization messages:
   ```
   Firebase initialized successfully
   Push notification service initialized
   ```

### Manual Permission Test

Add this to browser console to test permission request:
```javascript
// Test notification permission
if ('Notification' in window) {
  console.log('Notification permission:', Notification.permission);
  
  if (Notification.permission === 'default') {
    Notification.requestPermission().then(permission => {
      console.log('Permission result:', permission);
    });
  }
}
```

## Step 3: Test Through App UI

### Method 1: Use App's Built-in Permission Flow
1. Look for notification settings in the app
2. Navigate to notification settings page
3. Enable push notifications
4. Grant permission when prompted

### Method 2: Trigger Permission via App Component
Open browser console and run:
```javascript
// Access the app component and trigger notification setup
const appComponent = document.querySelector('app-root');
if (appComponent) {
  // This will trigger the permission flow
  appComponent.dispatchEvent(new CustomEvent('enableNotifications'));
}
```

## Step 4: Test Firebase Token Generation

In browser console:
```javascript
// Check if Firebase messaging is available
if (window.firebase && firebase.messaging) {
  const messaging = firebase.messaging();
  
  messaging.getToken({
    vapidKey: 'BE9BCB1mD6jOEL5wGM46BK4eVsVOTtuq4YmKEACpDqXJymeXQG7O2d8QBpN0NE9FBzsrZ3FnySi7unGKbqdQ1yQ'
  }).then(token => {
    console.log('FCM Token:', token);
  }).catch(err => {
    console.error('Error getting token:', err);
  });
}
```

## Step 5: Send Test Notification

### Method 1: Firebase Console
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `loyalty-test-project-58bcb`
3. Navigate to **Cloud Messaging**
4. Click **Send your first message**
5. Fill in notification details
6. Select **Send test message**
7. Paste the FCM token from Step 4

### Method 2: Using curl (if you have server access)
```bash
curl -X POST \
  https://fcm.googleapis.com/fcm/send \
  -H 'Authorization: key=YOUR_SERVER_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "to": "FCM_TOKEN_FROM_STEP_4",
    "notification": {
      "title": "Test Notification",
      "body": "This is a test from localhost!"
    },
    "data": {
      "actionUrl": "/dashboard"
    }
  }'
```

## Step 6: Test Background vs Foreground

### Foreground Test (App Active)
1. Keep the app tab active
2. Send notification from Firebase Console
3. Should see toast notification in app
4. Check console for: `Message received in foreground`

### Background Test (App Inactive)
1. Switch to different tab or minimize browser
2. Send notification from Firebase Console
3. Should see browser notification popup
4. Click notification to return to app

## Expected Behavior

### ✅ Success Indicators
- Service worker registered in DevTools
- Firebase token generated
- Permission granted
- Notifications appear as toast (foreground) or browser notification (background)
- Console shows Firebase initialization messages

### ❌ Troubleshooting

**Service Worker Not Registered**
```bash
# Check if file exists
ls -la projects/lp-client/src/firebase-messaging-sw.js

# Rebuild and serve
ng build && ng serve
```

**Permission Denied**
- Clear browser data for localhost
- Try incognito mode
- Check if HTTPS is required (some browsers)

**No Token Generated**
- Check Firebase config in service worker
- Verify VAPID key is correct
- Check browser console for errors

**No Notifications Received**
- Verify token is registered with backend
- Check Firebase project settings
- Confirm notification payload format

## Debug Commands

```javascript
// Check service worker status
navigator.serviceWorker.getRegistrations().then(regs => {
  console.log('Service workers:', regs);
});

// Check notification permission
console.log('Notification permission:', Notification.permission);

// Test basic notification
if (Notification.permission === 'granted') {
  new Notification('Test', { body: 'Local test notification' });
}
```

## Production Notes

- Firebase config uses test project credentials
- For production, update Firebase config in both:
  - `projects/lp-client/src/firebase-messaging-sw.js`
  - `projects/lp-client/src/app/services/push-notification.service.ts`
- Ensure HTTPS in production (required for service workers)
- Test on mobile devices using local IP address