#!/bin/bash

# Start Notification Backend Server
echo "🔔 Starting Push Notification Backend Server..."

# Check if we're in the right directory
if [ ! -f "angular.json" ]; then
    echo "❌ Error: angular.json not found. Please run this script from the project root."
    exit 1
fi

# Check if the test server exists
if [ ! -f "projects/lp-go/src/notifications/test-server.js" ]; then
    echo "❌ Error: Notification test server not found"
    exit 1
fi

echo "✅ Notification test server found"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Start the notification backend server
echo "🚀 Starting notification backend server on port 3000..."
echo ""
echo "📋 Available endpoints:"
echo "  POST http://localhost:3000/api/notifications/register-device"
echo "  POST http://localhost:3000/api/notifications/send"
echo "  POST http://localhost:3000/api/notifications/topics/subscribe"
echo "  POST http://localhost:3000/api/notifications/topics/unsubscribe"
echo "  GET  http://localhost:3000/health"
echo ""
echo "🔧 To test topic subscription:"
echo "curl -X POST http://localhost:3000/api/notifications/topics/subscribe \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"token\":\"test-token\",\"topic\":\"rmc_dev\"}'"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the server
cd projects/lp-go/src/notifications
node test-server.js
