# Push Notification Testing Guide

This guide will help you test push notifications in your application. We've fixed the subscription issue, but we need to verify that notifications are properly displayed.

## Prerequisites

1. **Backend Server**: Make sure the notification backend server is running
2. **Firebase Configuration**: Ensure Firebase is properly configured in your environment
3. **Browser Support**: Use Chrome or Firefox for testing (Safari has limited support)

## Testing Steps

### 1. Start the Backend Server

```bash
./start-notification-backend.sh
```

This will start the notification backend server on port 3000.

### 2. Start the Frontend Application

```bash
./test-localhost-push.sh
```

This will start the frontend on port 8100 with proxy configuration to forward API calls to the backend.

### 3. Open the Application in Browser

Open http://localhost:8100 in Chrome or Firefox.

### 4. Check Service Worker Registration

1. Open browser developer tools (F12)
2. Go to the "Application" tab
3. Select "Service Workers" in the left sidebar
4. Verify that `firebase-messaging-sw.js` is registered and active

### 5. Get FCM Token

1. Log in to the application
2. Open browser console
3. Look for messages about Firebase initialization
4. Find the FCM token (it should be logged to console or available as `window.fcmToken`)
5. Copy this token for testing

### 6. Subscribe to a Topic

1. Navigate to notification settings in the app
2. Subscribe to the "rmc_dev" topic
3. Check console for success message

### 7. Send Test Notification

Use the provided test script to send a notification:

```bash
# To send to a specific token
node test-send-notification.js --token "your-fcm-token-here"

# To send to a topic
node test-send-notification.js --topic "rmc_dev"
```

If you don't have a server key, you can use the Firebase Console:

1. Go to Firebase Console > Project Settings > Cloud Messaging
2. Use the "Send your first message" option
3. Create a test message targeting your token or topic

### 8. Verify Notification Reception

#### For Foreground Messages:
- The notification should appear as a toast in the application
- Check console for message reception logs

#### For Background Messages:
- Minimize the browser or navigate to another tab
- The notification should appear as a browser notification
- Click the notification to navigate back to the app

## Alternative Testing Method

We've created a standalone test page that you can use to test notifications:

1. Open `test-notification-flow.html` in your browser
2. Follow the steps in the page to:
   - Initialize Firebase
   - Request permission
   - Get FCM token
   - Subscribe to a topic
   - Send test notifications

## Troubleshooting

If notifications aren't showing:

1. **Check Console Errors**: Look for any errors in the browser console
2. **Verify Service Worker**: Make sure the service worker is registered and active
3. **Check Notification Permission**: Ensure notification permission is granted
4. **Test with Different Browsers**: Try Chrome, Firefox, and Edge
5. **Check Firebase Configuration**: Ensure the Firebase config matches between service worker and app
6. **Verify Token Generation**: Make sure a valid FCM token is being generated
7. **Test Both Foreground and Background**: Test with app in foreground and background
8. **Check Network Requests**: Look for any failed network requests in the Network tab

## Common Issues

1. **Service Worker Not Registered**: Make sure the service worker path is correct
2. **Permission Denied**: User must grant notification permission
3. **Token Not Generated**: Check for errors in token generation
4. **CORS Issues**: Backend must have proper CORS headers
5. **Firebase Configuration Mismatch**: Config must match between app and service worker
6. **Missing VAPID Key**: Web push requires a valid VAPID key
7. **Service Worker Scope Issues**: Service worker must be in the root directory

## Next Steps

Once you've verified that notifications are working:

1. **Implement Production Backend**: Replace the test server with a production-ready backend
2. **Configure Firebase for Production**: Set up proper Firebase credentials for production
3. **Add Analytics**: Track notification delivery and open rates
4. **Implement Rich Notifications**: Add images and action buttons to notifications
5. **Test on Mobile Devices**: Verify notifications work on iOS and Android

## Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Web Push Notifications Guide](https://developers.google.com/web/fundamentals/push-notifications)
- [Testing Push Notifications](https://firebase.google.com/docs/cloud-messaging/js/first-message)
- [Debugging Service Workers](https://developers.google.com/web/tools/workbox/guides/troubleshoot-and-debug)
