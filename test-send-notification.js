#!/usr/bin/env node

/**
 * Test script to send Firebase push notifications
 * This script sends actual Firebase messages to test the notification listeners
 */

const https = require('https');

// Firebase configuration (from your firebase-messaging-sw.js)
const FIREBASE_CONFIG = {
  projectId: "loyalty-test-project-58bcb",
  messagingSenderId: "798238467180",
  serverKey: "YOUR_SERVER_KEY_HERE" // You need to get this from Firebase Console
};

/**
 * Send a test notification using Firebase REST API
 */
async function sendTestNotification(token, topic = null) {
  const message = {
    notification: {
      title: "Test Notification",
      body: "This is a test message from the backend",
      icon: "/assets/icons/icon-192x192.png"
    },
    data: {
      topic: topic || "test",
      type: "test",
      id: `test-${Date.now()}`,
      actionUrl: "/notifications"
    }
  };

  // Add target (either token or topic)
  if (topic) {
    message.topic = topic;
  } else if (token) {
    message.token = token;
  } else {
    throw new Error('Either token or topic must be provided');
  }

  const payload = JSON.stringify({
    message: message
  });

  const options = {
    hostname: 'fcm.googleapis.com',
    port: 443,
    path: '/v1/projects/' + FIREBASE_CONFIG.projectId + '/messages:send',
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + FIREBASE_CONFIG.serverKey,
      'Content-Type': 'application/json',
      'Content-Length': payload.length
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ Notification sent successfully');
          console.log('Response:', data);
          resolve(JSON.parse(data));
        } else {
          console.error('❌ Failed to send notification');
          console.error('Status:', res.statusCode);
          console.error('Response:', data);
          reject(new Error(`HTTP ${res.statusCode}: ${data}`));
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ Request error:', error);
      reject(error);
    });

    req.write(payload);
    req.end();
  });
}

/**
 * Send notification to topic
 */
async function sendToTopic(topic) {
  console.log(`📤 Sending notification to topic: ${topic}`);
  try {
    await sendTestNotification(null, topic);
  } catch (error) {
    console.error('Error sending to topic:', error.message);
  }
}

/**
 * Send notification to specific token
 */
async function sendToToken(token) {
  console.log(`📤 Sending notification to token: ${token.substring(0, 20)}...`);
  try {
    await sendTestNotification(token);
  } catch (error) {
    console.error('Error sending to token:', error.message);
  }
}

/**
 * Simple test using curl (for when server key is not available)
 */
function showCurlExample(token, topic) {
  console.log('\n🔧 Manual testing with curl:');
  console.log('(You need to get your server key from Firebase Console > Project Settings > Cloud Messaging)');
  console.log('\nTo send to a specific token:');
  console.log(`curl -X POST https://fcm.googleapis.com/v1/projects/${FIREBASE_CONFIG.projectId}/messages:send \\`);
  console.log(`  -H "Authorization: Bearer YOUR_SERVER_KEY" \\`);
  console.log(`  -H "Content-Type: application/json" \\`);
  console.log(`  -d '{`);
  console.log(`    "message": {`);
  console.log(`      "token": "${token}",`);
  console.log(`      "notification": {`);
  console.log(`        "title": "Test Notification",`);
  console.log(`        "body": "This is a test message"`);
  console.log(`      },`);
  console.log(`      "data": {`);
  console.log(`        "topic": "test",`);
  console.log(`        "type": "test"`);
  console.log(`      }`);
  console.log(`    }`);
  console.log(`  }'`);
  
  if (topic) {
    console.log('\nTo send to a topic:');
    console.log(`curl -X POST https://fcm.googleapis.com/v1/projects/${FIREBASE_CONFIG.projectId}/messages:send \\`);
    console.log(`  -H "Authorization: Bearer YOUR_SERVER_KEY" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -d '{`);
    console.log(`    "message": {`);
    console.log(`      "topic": "${topic}",`);
    console.log(`      "notification": {`);
    console.log(`        "title": "Test Topic Notification",`);
    console.log(`        "body": "This is a test message to topic ${topic}"`);
    console.log(`      }`);
    console.log(`    }`);
    console.log(`  }'`);
  }
}

// Main execution
async function main() {
  console.log('🔔 Firebase Push Notification Test');
  console.log('==================================');
  
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage:');
    console.log('  node test-send-notification.js --token <FCM_TOKEN>');
    console.log('  node test-send-notification.js --topic <TOPIC_NAME>');
    console.log('  node test-send-notification.js --help');
    console.log('\nExample:');
    console.log('  node test-send-notification.js --token "your-fcm-token-here"');
    console.log('  node test-send-notification.js --topic "rmc_dev"');
    return;
  }
  
  if (args[0] === '--help') {
    console.log('This script sends test Firebase push notifications.');
    console.log('You need to configure your Firebase server key first.');
    console.log('\nTo get your server key:');
    console.log('1. Go to Firebase Console');
    console.log('2. Select your project');
    console.log('3. Go to Project Settings > Cloud Messaging');
    console.log('4. Copy the Server Key');
    console.log('5. Replace YOUR_SERVER_KEY_HERE in this script');
    return;
  }
  
  if (args[0] === '--token' && args[1]) {
    const token = args[1];
    if (FIREBASE_CONFIG.serverKey === 'YOUR_SERVER_KEY_HERE') {
      console.log('⚠️  Server key not configured. Showing curl example instead.');
      showCurlExample(token);
    } else {
      await sendToToken(token);
    }
  } else if (args[0] === '--topic' && args[1]) {
    const topic = args[1];
    if (FIREBASE_CONFIG.serverKey === 'YOUR_SERVER_KEY_HERE') {
      console.log('⚠️  Server key not configured. Showing curl example instead.');
      showCurlExample(null, topic);
    } else {
      await sendToTopic(topic);
    }
  } else {
    console.log('❌ Invalid arguments. Use --help for usage information.');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { sendTestNotification, sendToTopic, sendToToken };
