#!/usr/bin/env node

/**
 * Test Firebase configuration loading
 */

console.log('🔧 Testing Firebase Configuration Loading');
console.log('========================================');

// Simulate the environment loading
const fs = require('fs');
const path = require('path');

const envPath = path.join(__dirname, 'projects/lp-client/src/environments/environment.ts');

if (!fs.existsSync(envPath)) {
  console.log('❌ Environment file not found');
  process.exit(1);
}

const envContent = fs.readFileSync(envPath, 'utf8');

// Extract firebase config
const firebaseMatch = envContent.match(/firebase:\s*{([^}]+)}/s);

if (!firebaseMatch) {
  console.log('❌ Firebase configuration not found in environment');
  process.exit(1);
}

console.log('✅ Firebase configuration found in environment');

// Extract individual values
const config = firebaseMatch[1];
const extractValue = (key) => {
  const match = config.match(new RegExp(`${key}:\\s*["']([^"']+)["']`));
  return match ? match[1] : null;
};

const firebaseConfig = {
  apiKey: extractValue('apiKey'),
  authDomain: extractValue('authDomain'),
  projectId: extractValue('projectId'),
  storageBucket: extractValue('storageBucket'),
  messagingSenderId: extractValue('messagingSenderId'),
  appId: extractValue('appId'),
  measurementId: extractValue('measurementId'),
  vapidKey: extractValue('vapidKey')
};

console.log('\n📋 Firebase Configuration:');
console.log('==========================');
Object.entries(firebaseConfig).forEach(([key, value]) => {
  if (value) {
    console.log(`✅ ${key}: ${value.substring(0, 20)}...`);
  } else {
    console.log(`❌ ${key}: NOT FOUND`);
  }
});

// Check if all required fields are present
const requiredFields = ['apiKey', 'projectId', 'messagingSenderId', 'appId', 'vapidKey'];
const missingFields = requiredFields.filter(field => !firebaseConfig[field]);

if (missingFields.length === 0) {
  console.log('\n✅ All required Firebase configuration fields are present');
  console.log('\n🎉 Firebase configuration is ready for push notifications!');
  
  console.log('\n📋 Next Steps:');
  console.log('1. Run: ./fix-firebase-console-notifications.sh');
  console.log('2. Open http://localhost:8100 in browser');
  console.log('3. Check browser console for FCM token generation');
  console.log('4. Test Firebase Console notifications');
  
} else {
  console.log(`\n❌ Missing required fields: ${missingFields.join(', ')}`);
  console.log('Please add these fields to your environment.firebase configuration');
}

console.log('\n✅ Configuration test complete!');
