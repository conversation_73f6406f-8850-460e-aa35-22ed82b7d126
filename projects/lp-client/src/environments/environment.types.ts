export interface LssConfig {
  googleApiKey?: string;
  apiId?: string;
  appCode: string;
  appName: string;
  appVersion?: string;
  useAuth: boolean;
  useION: boolean;
  useISO: boolean;
  defaultNotAuthURL: string;
  autoLogout: boolean;
  autoLogoutTimeout: number;
  autoLogoutWarning: number;
  defaultLat?: number;
  defaultLng?: number;
  loadIdentity: boolean;
  identityBaseUrl: string;
  appBaseUrl: string;
  apiBaseUrl: string;
  configAPIUrl?: string;
  memberPhone?: {
    dialCode: string;
    nationalNumber: string;
  };
  memberCard?: string;
  telephone?: {
    selectFirstCountry: boolean;
    preferredCountries: string[];
    onlyCountries: string[];
  };
  theme?: {
    layout: string;
    backgroundImage: string;
    colours: {
      primary: string;
      primaryContrast: string;
      primaryShade: string;
      primaryTint: string;
      secondary: string;
      secondaryContrast: string;
      secondaryShade: string;
      secondaryTint: string;
    };
  };
  games?: {
    [key: string]: any;
  };
  navigation?: {
    routes: Array<{
      path: string;
      icon: string;
      label: string;
      main?: boolean;
      sidebar?: boolean;
      more?: boolean;
      exact?: boolean;
      link?: string;
    }>;
  };
  authConfig?: {
    [key: string]: any;
  };
  useDemoProfile?: boolean;
}

export interface Environment {
  production: boolean;
  env: string;
  client?: string;
  lssConfig: LssConfig;
  firebase?: {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    measurementId: string;
    vapidKey: string;
  };
  notificationApi?: {
    baseUrl: string;
  };
}
