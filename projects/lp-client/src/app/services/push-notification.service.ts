import { Injectable, NgZone } from '@angular/core';
import { Platform } from '@ionic/angular';
import { Router } from '@angular/router';
import { PushNotifications } from '@capacitor/push-notifications';
import { Preferences } from '@capacitor/preferences';
import { MemberService } from 'lp-client-api';
import { BehaviorSubject, Observable } from 'rxjs';
import { environment } from '../../environments/environment';

// For web
import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';

// Import notification models and services from the mobile-components library
import {
  NotificationData,
  NotificationPreferences,
  NotificationGroup,
  NotificationAnalyticsService
} from '@projects/mobile-components';

@Injectable({
  providedIn: 'root'
})
export class PushNotificationService {
  private deviceToken: string | null = null;
  private notificationSubject = new BehaviorSubject<NotificationData | null>(null);
  public notifications$ = this.notificationSubject.asObservable();

  // Default notification groups
  private defaultGroups: NotificationGroup[] = [
    {
      id: 'general',
      name: 'General Notifications',
      description: 'Important updates and announcements',
      defaultEnabled: true,
      icon: 'notifications-outline'
    },
    {
      id: 'promotions',
      name: 'Promotions & Offers',
      description: 'Special offers, discounts and promotions',
      defaultEnabled: true,
      icon: 'gift-outline'
    },
    {
      id: 'transactions',
      name: 'Transactions',
      description: 'Updates about your points and transactions',
      defaultEnabled: true,
      icon: 'card-outline'
    },
    {
      id: 'games',
      name: 'Games',
      description: 'Game-related notifications and rewards',
      defaultEnabled: true,
      icon: 'game-controller-outline'
    }
  ];

  // Add a flag to track authentication status
  private isAuthenticated = false;

  // Add a public getter for defaultGroups
  public get notificationGroups(): NotificationGroup[] {
    return this.defaultGroups;
  }

  /**
   * Get the current FCM registration token
   * @returns The FCM token or null if not available
   */
  public async getCurrentFCMToken(): Promise<string | null> {
    try {
      // First check if we have a stored token
      let token = localStorage.getItem('fcm_registration_token');
      
      if (token) {
        console.log('📋 Retrieved stored FCM token:', token);
        return token;
      }

      // If no stored token, try to get a new one
      if (this.firebaseMessaging && this.firebaseConfig.vapidKey) {
        console.log('🔄 Getting fresh FCM token...');
        token = await this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey);
        return token;
      }

      // If Firebase not initialized, check if we can initialize it
      if (!this.platform.is('capacitor') && Notification.permission === 'granted') {
        console.log('🚀 Initializing Firebase to get FCM token...');
        await this.safeInitializeFirebase();
        if (this.firebaseMessaging && this.firebaseConfig.vapidKey) {
          token = await this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey);
          return token;
        }
      }

      console.log('❌ No FCM token available');
      return null;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  }

  constructor(
    private platform: Platform,
    private memberService: MemberService,
    private zone: NgZone,
    private router: Router,
    private analyticsService: NotificationAnalyticsService
  ) {
    console.log('PushNotificationService constructor');
    // No automatic subscription to auth events to avoid interfering with login flow
  }

  // Add method to be called after authentication
  public setAuthenticated(status: boolean): void {
    this.isAuthenticated = status;

    // If we're authenticated and on web platform, initialize Firebase
    if (this.isAuthenticated && !this.platform.is('capacitor')) {
      this.initializeWeb();
    }
  }

  /**
   * Initialize push notifications
   * This is safe to call at app startup
   */
  async initialize(): Promise<void> {
    console.log('Initializing push notifications service');

    // Check if permissions were previously granted
    await this.checkAndRestorePermissions();

    // For native platforms, initialize immediately
    if (this.platform.is('capacitor')) {
      await this.initializeNative();
    } else {
      // For web, we'll initialize later with initializeAfterLogin
      console.log('Web platform detected, push notifications will be initialized after login is complete');
    }
  }

  /**
   * Check if notification permissions were previously granted and restore state
   */
  private async checkAndRestorePermissions(): Promise<void> {
    try {
      console.log('🔍 Checking for previous notification permissions...');
      
      // Check browser permission
      const browserPermission = Notification.permission === 'granted';
      
      // Check our stored preferences
      const storedPrefs = await this.getPreferences();
      
      // Check localStorage backup
      const permissionGranted = localStorage.getItem('notification_permission_granted') === 'true';
      
      console.log('Browser permission:', browserPermission);
      console.log('Stored preferences enabled:', storedPrefs.enabled);
      console.log('localStorage backup:', permissionGranted);
      
      // If browser permission is granted but our preferences say disabled, fix it
      if (browserPermission && (!storedPrefs.enabled || permissionGranted)) {
        console.log('🔧 Fixing notification preferences - browser permission granted but preferences disabled');
        storedPrefs.enabled = true;
        await this.savePreferences(storedPrefs);
        console.log('✅ Notification preferences restored');
      }
      
      // If permissions are enabled, try to initialize Firebase early
      if (browserPermission && storedPrefs.enabled && this.isAuthenticated) {
        console.log('🚀 Auto-initializing Firebase with existing permissions');
        setTimeout(() => {
          this.initializeWeb().catch(err => 
            console.log('Auto-initialization failed (non-critical):', err)
          );
        }, 2000);
      }
    } catch (error) {
      console.error('Error checking/restoring permissions:', error);
    }
  }

  /**
   * Initialize push notifications after login is complete
   * This should be called explicitly after the login process is fully complete
   */
  async initializeAfterLogin(): Promise<void> {
    console.log('Initializing push notifications after login');

    if (!this.platform.is('capacitor')) {
      // Only for web platform
      console.log('Setting authenticated status for web push notifications');
      this.setAuthenticated(true);
    }
  }

  /**
   * Request notification permission and initialize token if granted
   * This should be called explicitly by user action, not during authentication
   *
   * This implementation avoids service worker registration until after permission is granted
   */
  async requestPermission(): Promise<boolean> {
    console.log('Requesting push notification permission');
    let permissionGranted = false;

    if (this.platform.is('capacitor')) {
      const result = await PushNotifications.requestPermissions();
      permissionGranted = result.receive === 'granted';
    } else {
      try {
        // First check if permission is already granted - if so, just get token
        if (Notification.permission === 'granted') {
          console.log('Notification permission already granted');
          permissionGranted = true;
          
          // Only initialize Firebase if we don't already have messaging
          if (!this.firebaseMessaging) {
            try {
              await this.safeInitializeFirebase();
            } catch (firebaseError) {
              console.error('Error initializing Firebase for granted permission:', firebaseError);
            }
          }

          // Get token if we have Firebase initialized
          if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
            setTimeout(() => {
              if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
                this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey)
                  .catch((err: any) => console.error('Error getting token with granted permission:', err));
              }
            }, 1000);
          }

          return true;
        }

        // If permission not granted, request it WITHOUT initializing Firebase first
        console.log('Requesting notification permission from browser');
        
        const permission = await Notification.requestPermission();
        permissionGranted = permission === 'granted';
        
        console.log('Permission request result:', permission);

        // Only if permission was granted, then initialize Firebase
        if (permissionGranted) {
          console.log('Permission granted, initializing Firebase...');
          
          try {
            await this.safeInitializeFirebase();
            
            // Get token after Firebase is initialized
            if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
              setTimeout(() => {
                if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
                  this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey)
                    .catch((err: any) => console.error('Error getting token after permission granted:', err));
                }
              }, 1000);
            }
          } catch (firebaseError) {
            console.error('Error initializing Firebase after permission granted:', firebaseError);
            // Still return true since permission was granted
          }
        }
      } catch (permError) {
          console.error('Error during permission request:', permError);
          
          // Multiple fallbacks to check permission state
          
          // Fallback 1: Check directly
          try {
            const permState = Notification.permission;
            permissionGranted = permState === 'granted' as NotificationPermission;
            console.log('Fallback permission check result:', permState);
          } catch (fallbackError) {
            console.error('Error in fallback permission check:', fallbackError);
            
            // Fallback 2: Try creating a notification test (will fail silently if not permitted)
            try {
              if ('Notification' in window) {
                // Create with try-catch to avoid errors
                try { new Notification('Test', { silent: true }); permissionGranted = true; } 
                catch (e) { permissionGranted = false; }
              }
            } catch (testError) {
              console.error('Error in notification test:', testError);
              permissionGranted = false;
            }
          }
        }

        // If permission granted, get the token
        try {
          if (permissionGranted) {
            console.log('Permission granted, initializing token');
            
            // Double-check Firebase initialization
            if (!this.firebaseMessaging) {
              try {
                console.log('Re-initializing Firebase after permission grant');
                await this.safeInitializeFirebase();
              } catch (reinitError) {
                console.error('Error re-initializing Firebase after permission grant:', reinitError);
                // Continue anyway, we'll try to get messaging
              }
            }
            
            // Final check if messaging is available
            if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
              // Get token using the existing Firebase messaging instance
              // Use a longer timeout to avoid immediate page refreshs
              setTimeout(async () => {
                if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
                  await this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey);
                }
              }, 5000);
            } else {
              console.error('Firebase messaging not available after permission grant');
            }
          }
      } catch (error) {
        console.error('Error in notification permission process:', error);
        permissionGranted = false;
      } finally {
        // Clean up
        localStorage.removeItem('notification_permission_timestamp');
      }
    }

    // Track permission result
    try {
      await this.analyticsService?.trackPermissionChange(permissionGranted);
    } catch (analyticsError) {
      console.error('Error tracking permission change:', analyticsError);
    }

    // CRITICAL: Immediately save preferences if permission granted
    if (permissionGranted) {
      try {
        console.log('🔥 Permission granted! Saving preferences immediately...');
        const currentPrefs = await this.getPreferences();
        currentPrefs.enabled = true;
        await this.savePreferences(currentPrefs);
        console.log('✅ Preferences saved successfully after permission grant');
        
        // Also store in localStorage as backup
        localStorage.setItem('notification_permission_granted', 'true');
        localStorage.setItem('notification_permission_timestamp', Date.now().toString());
      } catch (saveError) {
        console.error('❌ Error saving preferences after permission grant:', saveError);
      }
    }

    return permissionGranted;
  }

  /**
   * Handle web notification received from Firebase
   * @param payload Firebase notification payload
   */
  private handleWebNotification(payload: any): void {
    console.log('Handling web notification:', payload);
    
    try {
      // Convert Firebase payload to common NotificationData format
      const notificationData: NotificationData = {
        title: payload.notification?.title || 'New Notification',
        body: payload.notification?.body || '',
        image: payload.notification?.image || undefined,
        data: payload.data || {}
      };
      
      // Extract action URL if present
      if (payload.data?.actionUrl) {
        notificationData.data.actionUrl = payload.data.actionUrl;
      }
      
      // Add important fields for tracking
      if (payload.data?.id) {
        notificationData.data.id = payload.data.id;
      }
      
      if (payload.data?.group) {
        notificationData.data.group = payload.data.group;
      }

      // Track notification delivery within zone to ensure proper Angular context
      if (payload.data?.id) {
        this.zone.run(() => {
          this.analyticsService?.trackDelivery(
            payload.data.id,
            payload.data?.group
          ).catch(err => console.error('Error tracking notification delivery:', err));
        });
      }

      // Emit the notification to be displayed within Angular zone
      // This ensures proper change detection and UI updates
      this.zone.run(() => {
        this.notificationSubject.next(notificationData);
      });
      
      // Play notification sound if available and app is in foreground
      this.playNotificationSound();
      
      // Create a browser notification if we're in background
      if (document.visibilityState !== 'visible') {
        this.createBrowserNotification(notificationData);
      }
    } catch (error) {
      console.error('Error handling web notification:', error);
    }
  }
  
  /**
   * Helper method to create a browser notification when app is in background
   * @param data notification data to display
   */
  private createBrowserNotification(data: NotificationData): void {
    // Check browser notification permission
    if (Notification.permission !== 'granted') {
      return;
    }
    
    try {
      const options: NotificationOptions = {
        body: data.body,
        icon: '/assets/icons/icon-192x192.png', // Default icon
        badge: '/assets/icons/badge-96x96.png',  // Default badge
        // Note: image is not a standard property in NotificationOptions
        // so we use a valid property instead
        data: {
          ...data.data,
          imageUrl: data.image // Store image URL in data
        },
        requireInteraction: true,
        silent: false
      };
      
      const notification = new Notification(data.title, options);
      
      // Handle click on the notification
      notification.onclick = () => {
        console.log('Browser notification clicked:', data);
        
        // Focus window
        window.focus();
        
        // Handle action URL if present
        if (data.data?.actionUrl) {
          this.zone.run(() => {
            this.router.navigateByUrl(data.data.actionUrl);
          });
        }
        
        // Track notification open
        if (data.data?.id) {
          this.zone.run(() => {
            this.analyticsService?.trackOpen(
              data.data.id,
              data.data?.group
            ).catch(err => console.error('Error tracking notification open:', err));
          });
        }
        
        // Close the notification
        notification.close();
      };
    } catch (error) {
      console.error('Error creating browser notification:', error);
    }
  }
  
  /**
   * Play a notification sound if available
   */
  private playNotificationSound(): void {
    try {
      const audio = new Audio('/assets/sounds/notification.mp3');
      audio.volume = 0.5;
      audio.play().catch(error => {
        // Browsers often block autoplay, so we just log this
        console.log('Could not play notification sound:', error);
      });
    } catch (error) {
      console.log('Error playing notification sound:', error);
    }
  }

  /**
   * Handle notifications received on native platforms
   * @param notification Capacitor notification
   */
  private handleNotificationReceived(notification: any): void {
    console.log('Handling native notification:', notification);
    
    try {
      // Convert Capacitor notification to common NotificationData format
      const notificationData: NotificationData = {
        title: notification.title || '',
        body: notification.body || '',
        image: notification.data?.imageUrl,
        data: notification.data || {}
      };

      // Track notification delivery
      if (notification.data?.id) {
        this.analyticsService?.trackDelivery(
          notification.data.id,
          notification.data?.group
        ).catch(err => console.error('Error tracking notification delivery:', err));
      }

      // Emit the notification to be displayed
      this.notificationSubject.next(notificationData);
    } catch (error) {
      console.error('Error handling native notification:', error);
    }
  }

  /**
   * Handle notification actions performed on native platforms
   * @param action Capacitor notification action
   */
  private handleNotificationAction(action: any): void {
    console.log('Handling notification action:', action);
    
    try {
      const notification = action.notification;
      
      // Track notification open
      if (notification.data?.id) {
        this.analyticsService?.trackOpen(
          notification.data.id,
          notification.data?.group
        ).catch(err => console.error('Error tracking notification open:', err));
      }

      // Handle action - typically navigation
      if (notification.data?.actionUrl) {
        this.zone.run(() => {
          this.router.navigateByUrl(notification.data.actionUrl);
        });
      }
    } catch (error) {
      console.error('Error handling notification action:', error);
    }
  }

  /**
   * Safely initialize Firebase without causing page refreshes
   * This method ensures Firebase is only initialized once and handles
   * the service worker registration in a way that doesn't disrupt authentication
   */
  private async safeInitializeFirebase(): Promise<void> {
    // If Firebase is already initialized, don't do it again
    if (this.firebaseApp && this.firebaseMessaging) {
      console.log('Firebase already initialized, reusing existing instance');
      return;
    }

    // Store original methods but don't override them aggressively
    console.log('🔧 Starting safe Firebase initialization...');

    try {
      console.log('Safely initializing Firebase');
      
      // Store auth state before we proceed with potentially disruptive operations
      const authStateBeforeInit = this.storeCurrentAuthTokens();

      // Conservative service worker registration - only if not already registered
      if ('serviceWorker' in navigator) {
        try {
          // Check if firebase-messaging-sw.js is already registered
          const registrations = await navigator.serviceWorker.getRegistrations();
          const isFirebaseSwRegistered = registrations.some(
            reg => reg.scope.includes(location.origin) && 
                  (reg.active?.scriptURL.includes('firebase-messaging-sw.js'))
          );

          if (!isFirebaseSwRegistered) {
            console.log('Firebase service worker not found, registering without page refresh...');
            
            // Add event listener to prevent automatic refresh on controllerchange
            const preventRefresh = () => {
              console.log('Service worker controller change detected - not refreshing');
            };
            
            navigator.serviceWorker.addEventListener('controllerchange', preventRefresh, { once: true });
            
            // Register the service worker
            const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
            console.log('Firebase service worker registered:', registration.scope);
            
            // Wait for it to be ready without triggering refresh
            await navigator.serviceWorker.ready;
            console.log('Firebase service worker is ready');
            
            // Remove the event listener
            navigator.serviceWorker.removeEventListener('controllerchange', preventRefresh);
            
          } else {
            console.log('Firebase service worker already registered');
          }
        } catch (swError) {
          console.error('Error with service worker registration:', swError);
          // Continue without service worker if it fails
        }
      }

      // Initialize Firebase with controlled service worker registration
      try {
        console.log('Creating Firebase app instance');
        this.firebaseApp = initializeApp(this.firebaseConfig);
      } catch (firebaseInitError: any) {
        // Handle case where app is already initialized
        if (firebaseInitError.code === 'app/duplicate-app') {
          console.log('Firebase app already exists, retrieving existing instance');
          const { getApp } = await import('firebase/app');
          try {
            this.firebaseApp = getApp();
          } catch (getAppError) {
            console.error('Error retrieving existing Firebase app:', getAppError);
            // Fallback: Create a new app with a unique name
            const { initializeApp: initApp } = await import('firebase/app');
            this.firebaseApp = initApp(this.firebaseConfig, 'dynamicApp' + Date.now());
          }
        } else {
          console.error('Firebase initialization error:', firebaseInitError);
          throw firebaseInitError;
        }
      }
      
      // Use a longer timeout before getting messaging to allow service worker to stabilize
      await new Promise(resolve => setTimeout(resolve, 2500));
      
      try {
        console.log('Getting Firebase messaging instance');
        this.firebaseMessaging = getMessaging(this.firebaseApp);
        
        // Listen for foreground messages
        onMessage(this.firebaseMessaging, (payload) => {
          console.log('Message received in foreground:', payload);
          this.handleWebNotification(payload);
        });
      } catch (messagingError) {
        console.error('Error getting Firebase messaging:', messagingError);
        throw messagingError;
      }

      console.log('Firebase initialized successfully');
      
      // Verify auth integrity after initialization is complete
      this.verifyAuthenticationIntegrity(authStateBeforeInit);
    } catch (error) {
      console.error('Error initializing Firebase:', error);

      // If there was an error, clear the instances to allow retry
      this.firebaseApp = null;
      this.firebaseMessaging = null;
      throw error; // Re-throw the error for better error handling upstream
    } finally {
      console.log('✅ Firebase initialization process completed');
    }
  }

  async registerToken(token: string): Promise<void> {
    console.log('Registering device token:', token);
    this.deviceToken = token;

    // Determine platform
    let platform = 'web';
    if (this.platform.is('ios')) {
      platform = 'ios';
    } else if (this.platform.is('android')) {
      platform = 'android';
    }

    // Register with backend
    this.memberService.registerDeviceToken(token, platform).subscribe({
      next: (response) => console.log('Device token registered successfully', response),
      error: (error) => console.error('Error registering device token', error)
    });
  }

  private async initializeNative(): Promise<void> {
    console.log('Initializing push notifications for native platform');
    try {
      // Register with FCM
      await PushNotifications.register();

      // Add listeners for push notification events
      PushNotifications.addListener('registration', (token) => {
        console.log('Push registration success, token: ' + token.value);
        this.registerToken(token.value);
      });

      PushNotifications.addListener('registrationError', (error) => {
        console.error('Error on registration: ' + JSON.stringify(error));
      });

      PushNotifications.addListener('pushNotificationReceived', (notification) => {
        console.log('Push notification received: ' + JSON.stringify(notification));
        this.handleNotificationReceived(notification);
      });

      PushNotifications.addListener('pushNotificationActionPerformed', (action) => {
        console.log('Push notification action performed: ' + JSON.stringify(action));
        this.handleNotificationAction(action);
      });
    } catch (error) {
      console.error('Error initializing push notifications for native platform', error);
    }
  }

  // Store Firebase config from environment with fallback
  private firebaseConfig = {
    apiKey: environment.firebase?.apiKey || "AIzaSyAseqL6KTURHxDYvK7Wygl0ViRIDS1OM3c",
    authDomain: environment.firebase?.authDomain || "loyalty-test-project-58bcb.firebaseapp.com",
    projectId: environment.firebase?.projectId || "loyalty-test-project-58bcb",
    storageBucket: environment.firebase?.storageBucket || "loyalty-test-project-58bcb.firebasestorage.app",
    messagingSenderId: environment.firebase?.messagingSenderId || "798238467180",
    appId: environment.firebase?.appId || "1:798238467180:web:ddab11e43f82af1de86449",
    measurementId: environment.firebase?.measurementId || "G-8CCCGX5VTK",
    vapidKey: environment.firebase?.vapidKey || "BE9BCB1mD6jOEL5wGM46BK4eVsVOTtuq4YmKEACpDqXJymeXQG7O2d8QBpN0NE9FBzsrZ3FnySi7unGKbqdQ1yQ",
    serverKey: 'YOUR_FIREBASE_SERVER_KEY_HERE' // Get this from Firebase Console > Project Settings > Cloud Messaging
  };

  // Store Firebase app and messaging instances
  private firebaseApp: any = null;
  private firebaseMessaging: any = null;

  /**
   * Initialize Firebase for web platform without requesting permissions
   * This method initializes Firebase but does not request notification permissions
   * to avoid interfering with the authentication flow
   */
  private async initializeWeb(): Promise<void> {
    console.log('Initializing push notifications for web platform');

    try {
      // Use our safe initialization method
      await this.safeInitializeFirebase();

      // Check if permission is already granted
      if (Notification.permission === 'granted') {
        console.log('Notification permission already granted, getting token');
        // Use a timeout to avoid immediate page refreshes
        setTimeout(async () => {
          if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
            await this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey);
          }
        }, 2000);
      } else {
        console.log('Notification permission not granted yet, will request later');
        // We'll request permission later when the user explicitly agrees
      }
    } catch (error) {
      console.error('Error initializing Firebase for web', error);
    }
  }

  /**
   * Get Firebase token after permission is granted
   * This is separated from initializeWeb to avoid permission requests during initialization
   * Uses multiple safeguards to prevent page refreshes and authentication disruption
   * 
   * @returns Promise that resolves to the token or null if unsuccessful
   */
  private async getFirebaseToken(messaging: any, vapidKey: string) {
    try {
      // Store a timestamp to detect refreshes
      const tokenTimestamp = Date.now().toString();
      localStorage.setItem('fcm_token_timestamp', tokenTimestamp);
      
      // Add a small delay to allow any pending operations to complete
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Store authentication tokens before potentially disruptive operations
      const authTokensBeforeOperation = this.storeCurrentAuthTokens();
      
      console.log('Getting FCM token with vapidKey');
      
      // Verify we weren't refreshed during the delay
      if (localStorage.getItem('fcm_token_timestamp') !== tokenTimestamp) {
        console.warn('Page refreshed during token initialization');
        return null;
      }

      // Wrap the potentially disruptive operation in a try block with multiple fallbacks
      let token: string | null = null;
      
      try {
        // First attempt: Standard getToken call with vapidKey
        const getTokenPromise = getToken(messaging, {
          vapidKey: vapidKey
        });
        
        // Set a timeout to prevent hanging
        const timeoutPromise = new Promise<string | null>(resolve => 
          setTimeout(() => {
            console.warn('Token retrieval timed out');
            resolve(null); 
          }, 10000) // Longer timeout for more reliability
        );
        
        // Race the token retrieval against the timeout
        token = await Promise.race([getTokenPromise, timeoutPromise]);
      } catch (tokenError) {
        console.error('First token retrieval attempt failed:', tokenError);
        
        // Second attempt: Try again with a different approach if the first failed
        try {
          console.log('Trying alternative token retrieval approach');
          
          // Add delay before retry
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Try getting token without options first
          token = await getToken(messaging);
        } catch (altTokenError) {
          console.error('Alternative token retrieval failed:', altTokenError);
          token = null;
        }
      }
      
      // Process the token result
      if (token) {
        console.log('🎯 FCM Token received successfully!');
        console.log('📋 FCM Registration Token:', token);
        console.log('🔗 Use this token in Firebase Console to send test notifications');
        console.log('📱 Firebase Project: loyalty-test-project-58bcb');
        console.log('🌐 Firebase Console: https://console.firebase.google.com/project/loyalty-test-project-58bcb/messaging');
        
        // Also store in localStorage for easy access
        localStorage.setItem('fcm_token_for_testing', token);
        localStorage.setItem('fcm_registration_token', token);
        console.log('💾 Token saved to localStorage as "fcm_registration_token"');
        
        // Make token available globally for easy access
        (window as any).fcmToken = token;
        console.log('🌍 Token available globally as window.fcmToken');
        
        // Register the token with our backend
        await this.registerToken(token);
        
        // Verify authentication is still intact
        this.verifyAuthenticationIntegrity(authTokensBeforeOperation);
        
        return token;
      } else {
        console.warn('Failed to get FCM token or operation timed out');
        return null;
      }
    } catch (error) {
      console.error('Error getting FCM token', error);

      // Log more details about the error
      const fcmError = error as any;
      if (fcmError.code) {
        console.error('Error code:', fcmError.code);
      }
      if (fcmError.message) {
        console.error('Error message:', fcmError.message);
      }
      
      return null;
    } finally {
      // Clean up
      localStorage.removeItem('fcm_token_timestamp');
    }
  }
  
  /**
   * Store current auth tokens for later verification
   */
  private storeCurrentAuthTokens(): any {
    try {
      // This function will be implemented outside to avoid direct dependency
      // on KeycloakService, but we'll return a placeholder for now
      console.log('Storing current auth tokens for verification');
      return {
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Error storing auth tokens:', error);
      return null;
    }
  }
  
  /**
   * Verify authentication integrity after potentially disruptive operations
   */
  private verifyAuthenticationIntegrity(storedTokens: any): void {
    if (!storedTokens) return;
    
    console.log('Verifying authentication integrity');
    // This is a placeholder - the actual implementation would check if
    // the authentication is still valid and take recovery actions if needed
    const timeDiff = Date.now() - storedTokens.timestamp;
    console.log(`Authentication verification completed. Time elapsed: ${timeDiff}ms`);
  }


  /**
   * Get the user's notification preferences
   * This method retrieves stored preferences from device storage
   * or creates default preferences if none exist
   * 
   * @returns Promise that resolves to notification preferences
   */
  public async getPreferences(): Promise<NotificationPreferences> {
    try {
      const prefData = await Preferences.get({ key: 'notification_preferences' });
      
      if (prefData && prefData.value) {
        const storedPrefs = JSON.parse(prefData.value) as NotificationPreferences;
        
        // Ensure backwards compatibility and defaults
        if (!storedPrefs.groups || !Array.isArray(storedPrefs.groups)) {
          // Convert default groups to array of IDs
          storedPrefs.groups = this.defaultGroups.map(group => group.id);
        } else {
          // Make sure all default groups exist in saved preferences
          for (const defaultGroup of this.defaultGroups) {
            if (defaultGroup.defaultEnabled && !storedPrefs.groups.includes(defaultGroup.id)) {
              storedPrefs.groups.push(defaultGroup.id);
            }
          }
        }
        
        return storedPrefs;
      }
      
      // Create default preferences if none exist
      return {
        enabled: false,
        // Filter default groups to get IDs of enabled ones
        groups: this.defaultGroups
          .filter(group => group.defaultEnabled)
          .map(group => group.id)
      };
    } catch (error) {
      console.error('Error getting notification preferences:', error);
      
      // Return default preferences in case of error
      return {
        enabled: false,
        groups: this.defaultGroups
          .filter(group => group.defaultEnabled)
          .map(group => group.id)
      };
    }
  }
  
  /**
   * Save the user's notification preferences
   * This method stores preferences to device storage
   * 
   * @param preferences The notification preferences to save
   * @returns Promise that resolves when preferences are saved
   */
  public async savePreferences(preferences: NotificationPreferences): Promise<void> {
    try {
      await Preferences.set({
        key: 'notification_preferences',
        value: JSON.stringify(preferences)
      });
      
      console.log('Notification preferences saved successfully');
      
      // If notifications are enabled, ensure we have permission and a token
      if (preferences.enabled && !this.deviceToken) {
        if (!this.platform.is('capacitor')) {
          // For web, check if we need to request permission or get token
          if (Notification.permission === 'granted') {
            console.log('Preferences enabled, getting token without requesting permission');
            // Initialize if not already initialized
            if (!this.firebaseMessaging) {
              await this.safeInitializeFirebase();
            }
            
            if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
              setTimeout(() => {
                if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
                  this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey)
                    .catch((err: any) => console.error('Error getting token after preference change:', err));
                }
              }, 1000);
            }
          }
          // We don't request permission here automatically - only when user explicitly agrees
        }
      }
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      throw error;
    }
  }

  /**
   * Subscribe to a Firebase topic via backend API
   * @param token FCM token
   * @param topic Topic name
   * @returns Promise<boolean> Success status
   */
  public async subscribeToTopic(token: string, topic: string): Promise<boolean> {
    try {
      console.log(`🔔 Subscribing to topic: ${topic} with token: ${token.substring(0, 20)}...`);
      
      // Use backend API instead of deprecated Firebase IID API
      const response = await fetch('/api/notifications/topics/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          topic: topic
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Successfully subscribed to topic: ${topic}`, result);
        return true;
      } else {
        const error = await response.text();
        console.error(`❌ Failed to subscribe to topic: ${topic}`, error);
        return false;
      }
    } catch (error) {
      console.error(`Error subscribing to topic ${topic}:`, error);
      return false;
    }
  }

  /**
   * Unsubscribe from a Firebase topic via backend API
   * @param token FCM token
   * @param topic Topic name
   * @returns Promise<boolean> Success status
   */
  public async unsubscribeFromTopic(token: string, topic: string): Promise<boolean> {
    try {
      console.log(`🔕 Unsubscribing from topic: ${topic} with token: ${token.substring(0, 20)}...`);
      
      // Use backend API instead of deprecated Firebase IID API
      const response = await fetch('/api/notifications/topics/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          topic: topic
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Successfully unsubscribed from topic: ${topic}`, result);
        return true;
      } else {
        const error = await response.text();
        console.error(`❌ Failed to unsubscribe from topic: ${topic}`, error);
        return false;
      }
    } catch (error) {
      console.error(`Error unsubscribing from topic ${topic}:`, error);
      return false;
    }
  }

  /**
   * Get list of topics the token is subscribed to
   * @param token FCM token
   * @returns Promise<string[]> List of subscribed topics
   */
  public async getSubscribedTopics(token: string): Promise<string[]> {
    try {
      console.log(`📋 Getting subscribed topics for token: ${token.substring(0, 20)}...`);
      
      // For web platform, use Firebase REST API
      if (!this.platform.is('capacitor')) {
        const response = await fetch(`https://iid.googleapis.com/iid/info/${token}?details=true`, {
          method: 'GET',
          headers: {
            'Authorization': `key=${this.firebaseConfig.serverKey || 'YOUR_SERVER_KEY'}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          const topics = data.rel?.topics ? Object.keys(data.rel.topics) : [];
          console.log(`✅ Subscribed topics:`, topics);
          return topics;
        } else {
          console.error(`❌ Failed to get subscribed topics`, await response.text());
          return [];
        }
      } else {
        // For native platforms, return empty array for now
        console.log('Getting subscribed topics for native platforms not yet implemented');
        return [];
      }
    } catch (error) {
      console.error('Error getting subscribed topics:', error);
      return [];
    }
  }

  public async subscribeToTopicViaBackend(token: string, topic: string): Promise<boolean> {
    try {
      console.log(`🔔 Subscribing to topic via backend: ${topic} with token: ${token.substring(0, 20)}...`);
      
      // Use the notification backend server URL (proxied through frontend)
      const baseUrl = '/api/notifications';
      
      const response = await fetch(`${baseUrl}/topics/subscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          topic: topic
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Successfully subscribed to topic: ${topic}`, result);
        return true;
      } else {
        const error = await response.text();
        console.error(`❌ Failed to subscribe to topic: ${topic}`, error);
        return false;
      }
    } catch (error) {
      console.error(`Error subscribing to topic ${topic}:`, error);
      return false;
    }
  }

  public async unsubscribeFromTopicViaBackend(token: string, topic: string): Promise<boolean> {
    try {
      console.log(`🔕 Unsubscribing from topic via backend: ${topic} with token: ${token.substring(0, 20)}...`);
      
      const baseUrl = '/api/notifications';
      
      const response = await fetch(`${baseUrl}/topics/unsubscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          topic: topic
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Successfully unsubscribed from topic: ${topic}`, result);
        return true;
      } else {
        const error = await response.text();
        console.error(`❌ Failed to unsubscribe from topic: ${topic}`, error);
        return false;
      }
    } catch (error) {
      console.error(`Error unsubscribing from topic ${topic}:`, error);
      return false;
    }
  }
}
