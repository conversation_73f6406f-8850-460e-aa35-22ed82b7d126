import { defineConfig } from 'cypress';

export default defineConfig({
  e2e: {
    baseUrl: 'http://localhost:8100',
    supportFile: 'cypress/support/e2e.ts',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    fixturesFolder: 'cypress/fixtures',
    screenshotsFolder: 'cypress/screenshots',
    videosFolder: 'cypress/videos',
    downloadsFolder: 'cypress/downloads',
    
    // Test execution settings
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    pageLoadTimeout: 30000,
    
    // Viewport settings
    viewportWidth: 1280,
    viewportHeight: 720,
    
    // Video and screenshot settings
    video: true,
    screenshotOnRunFailure: true,
    
    // Browser settings
    chromeWebSecurity: false,
    
    // Retry settings
    retries: {
      runMode: 2,
      openMode: 0
    },
    
    // Test isolation
    testIsolation: true,
    
    // Performance testing
    experimentalMemoryManagement: true,
    
    setupNodeEvents(on, config) {
      // Performance monitoring
      on('before:browser:launch', (browser = {}, launchOptions) => {
        if (browser.name === 'chrome') {
          launchOptions.args.push('--disable-dev-shm-usage');
          launchOptions.args.push('--no-sandbox');
          launchOptions.args.push('--disable-gpu');
          
          // Enable performance monitoring
          launchOptions.args.push('--enable-precise-memory-info');
          launchOptions.args.push('--enable-performance-manager-debug-logging');
        }
        
        return launchOptions;
      });
      
      // Task for performance measurement
      on('task', {
        measurePerformance: (metrics) => {
          console.log('Performance metrics:', metrics);
          return null;
        },
        
        log: (message) => {
          console.log(message);
          return null;
        }
      });
      
      // Custom commands for accessibility testing
      on('task', {
        checkAccessibility: (violations) => {
          if (violations.length > 0) {
            console.table(violations);
          }
          return null;
        }
      });
      
      return config;
    },
  },
  
  component: {
    devServer: {
      framework: 'angular',
      bundler: 'webpack',
    },
    specPattern: '**/*.cy.ts',
    supportFile: 'cypress/support/component.ts',
  },
  
  // Environment variables
  env: {
    apiUrl: 'http://localhost:8100/api',
    testUser: '<EMAIL>',
    testPassword: 'password123',
    coverage: true
  }
});