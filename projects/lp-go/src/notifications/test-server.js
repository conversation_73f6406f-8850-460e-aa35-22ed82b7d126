const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

// Create Express app
const app = express();

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Mock device storage
const devices = [];

// Register device endpoint
app.post('/api/notifications/register-device', (req, res) => {
  const { token, platform } = req.body;
  
  if (!token || !platform) {
    return res.status(400).json({
      success: false,
      error: 'Token and platform are required'
    });
  }
  
  const existingDeviceIndex = devices.findIndex(d => d.token === token);
  
  if (existingDeviceIndex >= 0) {
    // Update existing device
    devices[existingDeviceIndex] = {
      ...devices[existingDeviceIndex],
      ...req.body,
      updatedAt: new Date()
    };
    
    console.log(`Device updated: ${token}`);
    
    return res.status(200).json({
      success: true,
      deviceId: devices[existingDeviceIndex].id
    });
  } else {
    // Create new device
    const device = {
      ...req.body,
      id: `device-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      status: 'active'
    };
    
    devices.push(device);
    
    console.log(`Device registered: ${token}`);
    
    return res.status(200).json({
      success: true,
      deviceId: device.id
    });
  }
});

// Send notification endpoint
app.post('/api/notifications/send', (req, res) => {
  const { notification, tokens } = req.body;
  
  if (!notification) {
    return res.status(400).json({
      success: false,
      error: 'Notification is required'
    });
  }
  
  console.log(`Sending notification: ${JSON.stringify(notification)}`);
  
  return res.status(200).json({
    success: true,
    successCount: tokens ? tokens.length : 0,
    failureCount: 0,
    notificationId: `notification-${Date.now()}`
  });
});

// Topic subscription endpoints
app.post('/api/notifications/topics/subscribe', (req, res) => {
  const { token, topic } = req.body;

  if (!token || !topic) {
    return res.status(400).json({
      success: false,
      error: 'Token and topic are required'
    });
  }

  console.log(`Mock: Subscribing token ${token.substring(0, 20)}... to topic: ${topic}`);

  // Mock successful subscription
  return res.status(200).json({
    success: true,
    message: `Subscribed to topic: ${topic}`
  });
});

// Topic unsubscription endpoint
app.post('/api/notifications/topics/unsubscribe', (req, res) => {
  const { token, topic } = req.body;

  if (!token || !topic) {
    return res.status(400).json({
      success: false,
      error: 'Token and topic are required'
    });
  }

  console.log(`Mock: Unsubscribing token ${token.substring(0, 20)}... from topic: ${topic}`);

  // Mock successful unsubscription
  return res.status(200).json({
    success: true,
    message: `Unsubscribed from topic: ${topic}`
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', service: 'push-notification-service' });
});

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
  console.log(`Available endpoints:`);
  console.log(`  POST /api/notifications/register-device`);
  console.log(`  POST /api/notifications/send`);
  console.log(`  POST /api/notifications/topics/subscribe`);
  console.log(`  POST /api/notifications/topics/unsubscribe`);
  console.log(`  GET  /health`);
});
