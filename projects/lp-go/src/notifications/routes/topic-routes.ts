import { Router, Request, Response } from 'express';
import * as admin from 'firebase-admin';
import { getFirebaseAdmin } from '../config/firebase-config';

const router = Router();

// Initialize Firebase Admin
const firebaseAdmin = getFirebaseAdmin();

// Subscribe to topic endpoint
router.post('/subscribe', async (req: Request, res: Response) => {
  try {
    const { token, topic } = req.body;
    
    if (!token || !topic) {
      return res.status(400).json({ 
        success: false, 
        error: 'Token and topic are required' 
      });
    }
    
    // Use Firebase Admin SDK to subscribe token to topic
    await firebaseAdmin.messaging().subscribeToTopic([token], topic);
    
    res.json({ success: true, message: `Subscribed to topic: ${topic}` });
  } catch (error: any) {
    console.error('Error subscribing to topic:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to subscribe to topic' 
    });
  }
});

// Unsubscribe from topic endpoint
router.post('/unsubscribe', async (req: Request, res: Response) => {
  try {
    const { token, topic } = req.body;
    
    if (!token || !topic) {
      return res.status(400).json({ 
        success: false, 
        error: 'Token and topic are required' 
      });
    }
    
    // Use Firebase Admin SDK to unsubscribe token from topic
    await firebaseAdmin.messaging().unsubscribeFromTopic([token], topic);
    
    res.json({ success: true, message: `Unsubscribed from topic: ${topic}` });
  } catch (error: any) {
    console.error('Error unsubscribing from topic:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to unsubscribe from topic' 
    });
  }
});

export default router;
