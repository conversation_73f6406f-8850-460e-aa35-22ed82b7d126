/**
 * Notification Routes
 *
 * This file defines the API routes for push notification functionality.
 */

import { Router } from 'express';
import { NotificationController } from '../controllers/notification-controller';
import topicRoutes from './topic-routes';

const router = Router();
const notificationController = new NotificationController();

/**
 * @route POST /api/notifications/register-device
 * @description Register a device for push notifications
 * @access Public
 */
router.post('/register-device', (req, res) => {
  notificationController.registerDevice(req, res);
});

/**
 * @route POST /api/notifications/send
 * @description Send a notification to specific devices
 * @access Private
 */
router.post('/send', (req, res) => {
  notificationController.sendNotification(req, res);
});

/**
 * @route PUT /api/notifications/groups
 * @description Update notification groups for a device
 * @access Public
 */
router.put('/groups', (req, res) => {
  notificationController.updateDeviceGroups(req, res);
});

/**
 * @route GET /api/notifications/analytics
 * @description Get notification analytics
 * @access Private
 */
router.get('/analytics', (req, res) => {
  notificationController.getAnalytics(req, res);
});

/**
 * @route /api/notifications/topics/*
 * @description Topic subscription routes
 * @access Public
 */
router.use('/topics', topicRoutes);

export default router;
