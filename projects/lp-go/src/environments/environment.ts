// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  firebase: {
    apiKey: "AIzaSyAseqL6KTURHxDYvK7Wygl0ViRIDS1OM3c",
    authDomain: "loyalty-test-project-58bcb.firebaseapp.com",
    projectId: "loyalty-test-project-58bcb",
    storageBucket: "loyalty-test-project-58bcb.firebasestorage.app",
    messagingSenderId: "798238467180",
    appId: "1:798238467180:web:ddab11e43f82af1de86449",
    measurementId: "G-8CCCGX5VTK",
    vapidKey: "BE9BCB1mD6jOEL5wGM46BK4eVsVOTtuq4YmKEACpDqXJymeXQG7O2d8QBpN0NE9FBzsrZ3FnySi7unGKbqdQ1yQ"
  },
  notificationApi: {
    baseUrl: 'http://localhost:8100/api/notifications'
  }
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
