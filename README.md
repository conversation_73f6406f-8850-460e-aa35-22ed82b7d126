# LpAngular

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 14.2.3.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:8100/`. The application will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.

## Original Project create steps

- Create the main workspace

> `ng new --create-application=false lp-angular`

- Init ionic as multi app
  > `ionic init --multi-app`
- Generate the first application, these steps will be used for any new ionic projects

  > `ng generate application lp-client`
  >
  > `ng add @ionic/angular --project=lp-client`
  >
  > `cd projects\lp-client`
  >
  > `ionic init`

- Add capacitor to the project

  > `npx cap init`

- create empty package.json
  > `cd ../../`
- Add Android and IOS
  > `ionic capacitor add android --project=lp-client` > `ionic capacitor add ios --project=lp-client` > `cd projects\lp-client`

## Run in the actual project that you need to run

- Copy the content to the agular/IOS folder
  > `ionic capacitor copy android` > `ionic capacitor copy ios`
- Edit the capacitor.config.ts
  > Change the webDir to the correct location `../../dist/lp-client` > `ionic capacitor open android`
- Live reload the application
  > `ionic capacitor run android -l --external`
- Sync the project after making any change to capacitor
  > `npx cap sync`

## Angular commands

ng g library components

- Build Library

  > `ng build --project=lp-client-api`
  >
  > `ng build --project=components`

- Create a new service in the lp-client-api project

  > `ng g service services/Member --project=lp-client-api`

- Generate new Object

  > `ng g class --project=lp-client-api`

- Generate a new Component in the mobile-components projet

  > `ng g c pages/test --project=mobile-components`

- Generate a new Page in the lp-client projet

  > `ng g c test --project=mobile-components`

- Install from a local library.
  > `npm install --save ./projects/keycloak`
  >
  > `npm install --save ./dist/lp-client-api`
  >
  > `npm install --save ./dist/mobile-components`

# Dev getting started

## 1. Install the following tools

- VSCode (recommended) or other IDE of choice
- Android Studio and/or Xcode
- NodeJS
- Angular Client
  - `npm i -g @angular/cli`
- Ionic & Capacitor
  - `npm i -g @ionic/cli`
  - `npm i -g @capacitor/core`
  - `npm i -g @capacitor/cli`

Checkout the project from git and import into chosen IDE

## 2. Build all the projects

> `ng build --project=third-party-fix`

> `ng build --project=lp-client-api`

> `ng build --project=mobile-components`

> `ng build --project=lp-client --configuration=ffbp`

## 3. To run the project locally in the browser

The core project for the app is lp-client.

- Start the background task to monitor for changes on lp-client-api

  > `ng build --project=lp-client-api --watch`

- Start the background task to monitor for changes on lp-client-api

  > `ng build --project=mobile-components --watch`

- Start the main project with a specific client
  > `ionic serve --project=lp-client --configuration=ffbp` > `ng run lp-client:serve:rmicqa --host=localhost --port=8100`

## 4. Working with the Android emulator

Make sure that the latest version of the Android SDK is downloaded and enabled in Android Studio

Add the follow environment variable to globally define the path to the Android SDK

> `C:\Users\<USER>\AppData\Local\Android\Sdk`

Navigate into the main project `\lp-client\`

Sync the latest changes

> `npx cap sync`

Open Android Studio, import `\..\projects\lp-client\android\` if not already imported and start the emulator

From inside the `\lp-client\` directory run the following to launch the app inside the emulator running with Android Studio

> `ionic capacitor run android -l --external`

## 5. Working with with the iOS emulator

# Deploy Instructions

## LP Client Online Deploy

> `ng build --project=third-party-fix`

> `ng build --project=lp-client-api`

> `ng build --project=mobile-components`

> `ng build --project=lp-client --configuration=rmic --base-href=/lp-mobile/`
