#!/bin/bash

# Test Firebase Push Notifications on Localhost
echo "🔔 Setting up Firebase Push Notifications Test on Localhost..."

# Check if we're in the right directory
if [ ! -f "angular.json" ]; then
    echo "❌ Error: angular.json not found. Please run this script from the project root."
    exit 1
fi

# Check if Firebase service worker exists
if [ ! -f "projects/lp-client/src/firebase-messaging-sw.js" ]; then
    echo "❌ Error: Firebase service worker not found at projects/lp-client/src/firebase-messaging-sw.js"
    exit 1
fi

echo "✅ Firebase service worker found"

# Check if push notification service exists
if [ ! -f "projects/lp-client/src/app/services/push-notification.service.ts" ]; then
    echo "❌ Error: Push notification service not found"
    exit 1
fi

echo "✅ Push notification service found"

# Build the application
echo "🏗️  Building lp-client application..."
ng build lp-client --configuration=development

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi

# Start the development server
echo "🚀 Starting development server..."
echo ""
echo "📋 Testing Instructions:"
echo "1. Open http://localhost:8100 in Chrome or Firefox"
echo "2. Open Developer Tools (F12) > Application > Service Workers"
echo "3. Log in to the application"
echo "4. Look for Firebase initialization messages in console"
echo "5. Test notification permission through app settings"
echo ""
echo "🔧 Advanced Testing:"
echo "- Run: node test-push-notifications.js (to verify setup)"
echo "- Follow: test-push-notifications-localhost.md (for detailed testing)"
echo ""
echo "🌐 Server will start on http://localhost:8100"
echo "Press Ctrl+C to stop the server"
echo ""

# Start the server
ng serve lp-client --port=4200 --open