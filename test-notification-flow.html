<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Push Notification Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .token-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Push Notification Test</h1>
        
        <div id="status" class="status info">
            Initializing Firebase...
        </div>
        
        <div>
            <h3>Step 1: Initialize Firebase</h3>
            <button id="initBtn" onclick="initializeFirebase()">Initialize Firebase</button>
        </div>
        
        <div>
            <h3>Step 2: Request Notification Permission</h3>
            <button id="permissionBtn" onclick="requestPermission()" disabled>Request Permission</button>
        </div>
        
        <div>
            <h3>Step 3: Get FCM Token</h3>
            <button id="tokenBtn" onclick="getToken()" disabled>Get Token</button>
            <div id="tokenDisplay" class="token-display" style="display: none;"></div>
        </div>
        
        <div>
            <h3>Step 4: Subscribe to Topic</h3>
            <input type="text" id="topicInput" placeholder="Enter topic (e.g., rmc_dev)" value="rmc_dev">
            <button id="subscribeBtn" onclick="subscribeToTopic()" disabled>Subscribe to Topic</button>
        </div>
        
        <div>
            <h3>Step 5: Test Notification</h3>
            <button id="testBtn" onclick="testNotification()" disabled>Send Test Notification</button>
        </div>
        
        <div>
            <h3>Console Log</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>

    <script>
        let messaging;
        let currentToken;
        
        const firebaseConfig = {
            apiKey: "AIzaSyAseqL6KTURHxDYvK7Wygl0ViRIDS1OM3c",
            authDomain: "loyalty-test-project-58bcb.firebaseapp.com",
            projectId: "loyalty-test-project-58bcb",
            storageBucket: "loyalty-test-project-58bcb.firebasestorage.app",
            messagingSenderId: "798238467180",
            appId: "1:798238467180:web:ddab11e43f82af1de86449",
            measurementId: "G-8CCCGX5VTK"
        };
        
        const vapidKey = "BE9BCB1mD6jOEL5wGM46BK4eVsVOTtuq4YmKEACpDqXJymeXQG7O2d8QBpN0NE9FBzsrZ3FnySi7unGKbqdQ1yQ";
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }
        
        async function initializeFirebase() {
            try {
                log('Initializing Firebase...');
                firebase.initializeApp(firebaseConfig);
                messaging = firebase.messaging();
                
                // Listen for foreground messages
                messaging.onMessage((payload) => {
                    log('📨 Foreground message received: ' + JSON.stringify(payload));
                    updateStatus('Foreground notification received!', 'success');
                    
                    // Show browser notification
                    if (Notification.permission === 'granted') {
                        new Notification(payload.notification.title, {
                            body: payload.notification.body,
                            icon: '/assets/icons/icon-192x192.png'
                        });
                    }
                });
                
                log('✅ Firebase initialized successfully');
                updateStatus('Firebase initialized', 'success');
                document.getElementById('permissionBtn').disabled = false;
                
            } catch (error) {
                log('❌ Firebase initialization error: ' + error.message);
                updateStatus('Firebase initialization failed', 'error');
            }
        }
        
        async function requestPermission() {
            try {
                log('Requesting notification permission...');
                const permission = await Notification.requestPermission();
                
                if (permission === 'granted') {
                    log('✅ Notification permission granted');
                    updateStatus('Permission granted', 'success');
                    document.getElementById('tokenBtn').disabled = false;
                } else {
                    log('❌ Notification permission denied');
                    updateStatus('Permission denied', 'error');
                }
            } catch (error) {
                log('❌ Permission request error: ' + error.message);
                updateStatus('Permission request failed', 'error');
            }
        }
        
        async function getToken() {
            try {
                log('Getting FCM token...');
                currentToken = await messaging.getToken({ vapidKey: vapidKey });
                
                if (currentToken) {
                    log('✅ FCM token received: ' + currentToken.substring(0, 50) + '...');
                    document.getElementById('tokenDisplay').style.display = 'block';
                    document.getElementById('tokenDisplay').textContent = currentToken;
                    updateStatus('FCM token generated', 'success');
                    document.getElementById('subscribeBtn').disabled = false;
                    document.getElementById('testBtn').disabled = false;
                    
                    // Make token available globally
                    window.fcmToken = currentToken;
                } else {
                    log('❌ No FCM token available');
                    updateStatus('Failed to get FCM token', 'error');
                }
            } catch (error) {
                log('❌ Token generation error: ' + error.message);
                updateStatus('Token generation failed', 'error');
            }
        }
        
        async function subscribeToTopic() {
            const topic = document.getElementById('topicInput').value;
            if (!topic || !currentToken) {
                log('❌ Topic name and token required');
                return;
            }
            
            try {
                log(`Subscribing to topic: ${topic}...`);
                
                const response = await fetch('http://localhost:3000/api/notifications/topics/subscribe', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        token: currentToken,
                        topic: topic
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log(`✅ Successfully subscribed to topic: ${topic}`);
                    updateStatus(`Subscribed to ${topic}`, 'success');
                } else {
                    log(`❌ Subscription failed: ${result.error}`);
                    updateStatus('Subscription failed', 'error');
                }
            } catch (error) {
                log('❌ Subscription error: ' + error.message);
                updateStatus('Subscription failed', 'error');
            }
        }
        
        async function testNotification() {
            if (!currentToken) {
                log('❌ No FCM token available');
                return;
            }
            
            try {
                log('Sending test notification...');
                updateStatus('Sending test notification...', 'info');
                
                // This would normally be done from your backend
                // For testing, we'll show instructions
                log('📋 To send a test notification, use:');
                log(`node test-send-notification.js --token "${currentToken}"`);
                log('Or send to topic:');
                log(`node test-send-notification.js --topic "rmc_dev"`);
                
                updateStatus('Check console for test instructions', 'warning');
                
            } catch (error) {
                log('❌ Test notification error: ' + error.message);
                updateStatus('Test failed', 'error');
            }
        }
        
        // Auto-initialize on page load
        window.addEventListener('load', () => {
            log('🔔 Push Notification Test Page Loaded');
            log('Click "Initialize Firebase" to start');
        });
    </script>
</body>
</html>
