#!/bin/bash

if [ -z "$1" ] || [ -z "$2" ]; then
    echo "Missing Parameters. Usage: $0 {Client} {ENV} [ios|and]"
    exit 1
fi

client="$1"
env="${2}"
config_file="${client}${env}"

echo "config: ${config_file}"

# Attempt to build third-party-fix now that we've fixed the Angular 19 compatibility issues
ng build --project=third-party-fix

ng build --project=lp-client-api
ng build --project=mobile-components
ng build --project=lp-client --configuration="${config_file}"

# Run the Node.js script to trigger VSCode tasks
ng build --project=lp-client-api --watch

# ng build --project=mobile-components --watch

# ng run lp-client:serve:${client}${env} --host=localhost --port=8100
