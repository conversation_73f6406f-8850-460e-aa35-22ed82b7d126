#!/bin/bash

# Complete Push Notification Test Setup
echo "🔔 Complete Push Notification Test Setup"
echo "========================================"

# Check if we're in the right directory
if [ ! -f "angular.json" ]; then
    echo "❌ Error: angular.json not found. Please run this script from the project root."
    exit 1
fi

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to start backend server
start_backend() {
    echo "🚀 Starting notification backend server..."
    
    if check_port 3000; then
        echo "⚠️  Port 3000 is already in use. Stopping existing process..."
        pkill -f "node.*test-server.js" || true
        sleep 2
    fi
    
    cd projects/lp-go/src/notifications
    node test-server.js &
    BACKEND_PID=$!
    cd - > /dev/null
    
    # Wait for server to start
    sleep 3
    
    # Test if backend is running
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ Backend server started successfully on port 3000"
        return 0
    else
        echo "❌ Failed to start backend server"
        return 1
    fi
}

# Function to start frontend
start_frontend() {
    echo "🌐 Starting frontend application..."
    
    if check_port 4200; then
        echo "⚠️  Port 4200 is already in use. You may need to stop the existing Angular dev server."
        echo "   Run: pkill -f 'ng serve' to stop it."
    fi
    
    # Build first
    echo "🏗️  Building lp-client..."
    ng build lp-client --configuration=development
    
    if [ $? -eq 0 ]; then
        echo "✅ Build successful!"
        
        # Start dev server
        echo "🚀 Starting Angular dev server..."
        ng serve lp-client --port=4200 &
        FRONTEND_PID=$!
        
        echo "✅ Frontend will be available at http://localhost:4200"
        return 0
    else
        echo "❌ Build failed"
        return 1
    fi
}

# Function to test backend endpoints
test_backend() {
    echo "🧪 Testing backend endpoints..."
    
    # Test health endpoint
    echo "Testing health endpoint..."
    if curl -s http://localhost:3000/health | grep -q "ok"; then
        echo "✅ Health endpoint working"
    else
        echo "❌ Health endpoint failed"
        return 1
    fi
    
    # Test topic subscription
    echo "Testing topic subscription..."
    response=$(curl -s -X POST http://localhost:3000/api/notifications/topics/subscribe \
        -H 'Content-Type: application/json' \
        -d '{"token":"test-token-123","topic":"rmc_dev"}')
    
    if echo "$response" | grep -q "success.*true"; then
        echo "✅ Topic subscription endpoint working"
    else
        echo "❌ Topic subscription failed"
        echo "Response: $response"
        return 1
    fi
    
    return 0
}

# Cleanup function
cleanup() {
    echo ""
    echo "🧹 Cleaning up..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    pkill -f "node.*test-server.js" 2>/dev/null || true
    echo "✅ Cleanup complete"
}

# Set up trap for cleanup on exit
trap cleanup EXIT INT TERM

# Main execution
echo "1️⃣  Starting backend server..."
if start_backend; then
    echo ""
    echo "2️⃣  Testing backend..."
    if test_backend; then
        echo ""
        echo "3️⃣  Starting frontend..."
        start_frontend
        
        echo ""
        echo "🎉 Setup complete!"
        echo ""
        echo "📋 Testing Instructions:"
        echo "1. Open http://localhost:4200 in Chrome or Firefox"
        echo "2. Open Developer Tools (F12) > Console"
        echo "3. Log in to the application"
        echo "4. Go to notification settings"
        echo "5. Try to subscribe to a topic (e.g., 'rmc_dev')"
        echo "6. Check console for success messages"
        echo ""
        echo "🔧 Manual testing commands:"
        echo "curl -X POST http://localhost:3000/api/notifications/topics/subscribe \\"
        echo "  -H 'Content-Type: application/json' \\"
        echo "  -d '{\"token\":\"your-fcm-token\",\"topic\":\"rmc_dev\"}'"
        echo ""
        echo "Press Ctrl+C to stop all servers"
        
        # Keep script running
        wait
    else
        echo "❌ Backend tests failed"
        exit 1
    fi
else
    echo "❌ Failed to start backend"
    exit 1
fi
