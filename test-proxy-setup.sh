#!/bin/bash

# Test Proxy Setup for Push Notifications
echo "🔧 Testing Proxy Setup for Push Notifications"
echo "=============================================="

# Check if backend is running
echo "1️⃣  Checking if backend server is running on port 3000..."
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ Backend server is running on port 3000"
else
    echo "❌ Backend server is not running on port 3000"
    echo "   Please start it with: ./start-notification-backend.sh"
    exit 1
fi

# Check if frontend is running
echo ""
echo "2️⃣  Checking if frontend server is running on port 8100..."
if curl -s http://localhost:8100 > /dev/null; then
    echo "✅ Frontend server is running on port 8100"
else
    echo "❌ Frontend server is not running on port 8100"
    echo "   Please start it with: ./test-localhost-push.sh"
    exit 1
fi

# Test direct backend connection
echo ""
echo "3️⃣  Testing direct backend connection..."
response=$(curl -s -X POST http://localhost:3000/api/notifications/topics/subscribe \
    -H 'Content-Type: application/json' \
    -d '{"token":"test-token-direct","topic":"test_topic"}')

if echo "$response" | grep -q "success.*true"; then
    echo "✅ Direct backend connection working"
else
    echo "❌ Direct backend connection failed"
    echo "Response: $response"
fi

# Test proxied connection
echo ""
echo "4️⃣  Testing proxied connection through frontend..."
response=$(curl -s -X POST http://localhost:8100/api/notifications/topics/subscribe \
    -H 'Content-Type: application/json' \
    -d '{"token":"test-token-proxy","topic":"test_topic"}')

if echo "$response" | grep -q "success.*true"; then
    echo "✅ Proxied connection working"
    echo "🎉 Proxy setup is working correctly!"
else
    echo "❌ Proxied connection failed"
    echo "Response: $response"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "1. Make sure Angular dev server is started with proxy config:"
    echo "   ng serve lp-client --port=8100 --proxy-config=projects/lp-client/proxy.conf.json"
    echo "2. Check that proxy.conf.json exists in projects/lp-client/"
    echo "3. Restart the Angular dev server if you just added the proxy config"
fi

echo ""
echo "📋 Summary:"
echo "- Backend server: http://localhost:3000"
echo "- Frontend server: http://localhost:8100"
echo "- API calls from frontend will be proxied to backend"
echo "- Use /api/notifications/* URLs in your frontend code"
