#!/usr/bin/env node

/**
 * Diagnostic script to check push notification listener setup
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Push Notification Listener Diagnostic');
console.log('========================================');

// Check 1: Service Worker Registration
console.log('\n1️⃣  Checking Service Worker Setup...');
const swPath = path.join(__dirname, 'projects/lp-client/src/firebase-messaging-sw.js');
if (fs.existsSync(swPath)) {
  console.log('✅ Firebase service worker found');
  const swContent = fs.readFileSync(swPath, 'utf8');
  
  if (swContent.includes('onBackgroundMessage')) {
    console.log('✅ Background message handler found');
  } else {
    console.log('❌ Background message handler missing');
  }
  
  if (swContent.includes('showNotification')) {
    console.log('✅ Notification display code found');
  } else {
    console.log('❌ Notification display code missing');
  }
} else {
  console.log('❌ Firebase service worker not found');
}

// Check 2: Push Notification Service
console.log('\n2️⃣  Checking Push Notification Service...');
const servicePath = path.join(__dirname, 'projects/lp-client/src/app/services/push-notification.service.ts');
if (fs.existsSync(servicePath)) {
  console.log('✅ Push notification service found');
  const serviceContent = fs.readFileSync(servicePath, 'utf8');
  
  if (serviceContent.includes('onMessage')) {
    console.log('✅ Foreground message listener found');
  } else {
    console.log('❌ Foreground message listener missing');
  }
  
  if (serviceContent.includes('handleWebNotification')) {
    console.log('✅ Web notification handler found');
  } else {
    console.log('❌ Web notification handler missing');
  }
  
  if (serviceContent.includes('notificationSubject')) {
    console.log('✅ Notification subject (for toast) found');
  } else {
    console.log('❌ Notification subject missing');
  }
} else {
  console.log('❌ Push notification service not found');
}

// Check 3: Notification Toast Component
console.log('\n3️⃣  Checking Notification Toast Component...');
const toastPath = path.join(__dirname, 'projects/lp-client/src/app/components/notification-toast/notification-toast.component.ts');
if (fs.existsSync(toastPath)) {
  console.log('✅ Notification toast component found');
  const toastContent = fs.readFileSync(toastPath, 'utf8');
  
  if (toastContent.includes('notifications$.subscribe')) {
    console.log('✅ Toast subscribes to notification service');
  } else {
    console.log('❌ Toast does not subscribe to notification service');
  }
} else {
  console.log('❌ Notification toast component not found');
}

// Check 4: App Component Integration
console.log('\n4️⃣  Checking App Component Integration...');
const appPath = path.join(__dirname, 'projects/lp-client/src/app/app.component.ts');
if (fs.existsSync(appPath)) {
  console.log('✅ App component found');
  const appContent = fs.readFileSync(appPath, 'utf8');
  
  if (appContent.includes('PushNotificationService')) {
    console.log('✅ App component imports PushNotificationService');
  } else {
    console.log('❌ App component does not import PushNotificationService');
  }
  
  if (appContent.includes('initializePushNotifications')) {
    console.log('✅ App component initializes push notifications');
  } else {
    console.log('❌ App component does not initialize push notifications');
  }
} else {
  console.log('❌ App component not found');
}

// Check 5: App Template Integration
console.log('\n5️⃣  Checking App Template Integration...');
const appTemplatePath = path.join(__dirname, 'projects/lp-client/src/app/app.component.html');
if (fs.existsSync(appTemplatePath)) {
  console.log('✅ App template found');
  const templateContent = fs.readFileSync(appTemplatePath, 'utf8');
  
  if (templateContent.includes('app-notification-toast')) {
    console.log('✅ Notification toast component included in app template');
  } else {
    console.log('❌ Notification toast component not included in app template');
  }
} else {
  console.log('❌ App template not found');
}

// Check 6: Firebase Configuration
console.log('\n6️⃣  Checking Firebase Configuration...');
const envPath = path.join(__dirname, 'projects/lp-client/src/environments/environment.ts');
if (fs.existsSync(envPath)) {
  console.log('✅ Environment file found');
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  if (envContent.includes('firebase')) {
    console.log('✅ Firebase configuration found in environment');
  } else {
    console.log('❌ Firebase configuration missing from environment');
  }
  
  if (envContent.includes('vapidKey')) {
    console.log('✅ VAPID key found');
  } else {
    console.log('❌ VAPID key missing');
  }
} else {
  console.log('❌ Environment file not found');
}

// Generate test instructions
console.log('\n🧪 Testing Instructions:');
console.log('========================');
console.log('1. Start the notification backend:');
console.log('   ./start-notification-backend.sh');
console.log('');
console.log('2. Start the frontend:');
console.log('   ng serve lp-client --port=4200');
console.log('');
console.log('3. Open browser and check console for:');
console.log('   - "Firebase service worker registered"');
console.log('   - "Firebase messaging initialized"');
console.log('   - FCM token generation');
console.log('');
console.log('4. Test notification reception:');
console.log('   a) Get your FCM token from browser console (window.fcmToken)');
console.log('   b) Use the test script:');
console.log('      node test-send-notification.js --token "your-token-here"');
console.log('');
console.log('5. Check for notification display:');
console.log('   - Browser notification (if app is in background)');
console.log('   - Toast notification (if app is in foreground)');
console.log('   - Console messages showing message reception');

// Generate debugging checklist
console.log('\n🐛 Debugging Checklist:');
console.log('=======================');
console.log('If notifications are not showing:');
console.log('');
console.log('□ Check browser console for errors');
console.log('□ Verify service worker is registered and active');
console.log('□ Confirm FCM token is generated');
console.log('□ Test with browser developer tools > Application > Service Workers');
console.log('□ Check notification permissions in browser settings');
console.log('□ Verify Firebase project configuration matches');
console.log('□ Test both foreground and background message reception');
console.log('□ Check if notification toast component is properly subscribed');

console.log('\n✅ Diagnostic complete!');
